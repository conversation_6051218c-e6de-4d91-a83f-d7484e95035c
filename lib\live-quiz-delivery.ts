import { getSocketClient } from './socket-client'
import { prisma } from './prisma'

interface QuestionDeliveryOptions {
  sessionId: string
  questionIndex: number
  timeLimit?: number
  autoAdvance?: boolean
}

interface ParticipantProgress {
  userId: string
  currentQuestion: number
  score: number
  timeSpent: number
  lastActivity: Date
}

export class LiveQuizDeliveryService {
  private static instance: LiveQuizDeliveryService
  private activeTimers: Map<string, NodeJS.Timeout> = new Map()
  private participantProgress: Map<string, Map<string, ParticipantProgress>> = new Map()

  static getInstance(): LiveQuizDeliveryService {
    if (!LiveQuizDeliveryService.instance) {
      LiveQuizDeliveryService.instance = new LiveQuizDeliveryService()
    }
    return LiveQuizDeliveryService.instance
  }

  /**
   * Start synchronized question delivery for a live quiz session
   */
  async startQuestionDelivery(options: QuestionDeliveryOptions): Promise<void> {
    const { sessionId, questionIndex, timeLimit, autoAdvance } = options

    try {
      // Get session and question data
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            include: {
              questions: {
                where: { order: questionIndex },
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  points: true,
                  order: true,
                  image: true
                }
              }
            }
          },
          participants: {
            where: { isActive: true },
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          }
        }
      })

      if (!session || session.quiz.questions.length === 0) {
        throw new Error('Session or question not found')
      }

      const question = session.quiz.questions[0]
      const socketClient = getSocketClient()

      // Broadcast question to all participants
      socketClient.broadcastLiveQuizEvent('live-quiz:question-delivered', {
        sessionId,
        questionData: question,
        questionIndex,
        timeLimit,
        deliveredAt: new Date(),
        totalQuestions: await this.getTotalQuestions(session.quizId)
      })

      // Initialize participant progress tracking
      this.initializeParticipantProgress(sessionId, session.participants)

      // Set up auto-advance timer if enabled
      if (autoAdvance && timeLimit) {
        this.scheduleAutoAdvance(sessionId, timeLimit)
      }

      // Start progress monitoring
      this.startProgressMonitoring(sessionId)

    } catch (error) {
      console.error('Error starting question delivery:', error)
      throw error
    }
  }

  /**
   * Advance to the next question
   */
  async advanceQuestion(sessionId: string): Promise<void> {
    try {
      // Clear any existing timer
      this.clearAutoAdvanceTimer(sessionId)

      // Get current session state
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        select: {
          currentQuestion: true,
          questionTimeLimit: true,
          autoAdvance: true,
          quiz: {
            select: {
              questions: {
                select: { id: true, order: true },
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      if (!session) {
        throw new Error('Session not found')
      }

      const nextQuestionIndex = session.currentQuestion + 1
      const hasNextQuestion = nextQuestionIndex < session.quiz.questions.length

      if (hasNextQuestion) {
        // Update session current question
        await prisma.liveQuizSession.update({
          where: { id: sessionId },
          data: { currentQuestion: nextQuestionIndex }
        })

        // Start delivery for next question
        await this.startQuestionDelivery({
          sessionId,
          questionIndex: nextQuestionIndex,
          timeLimit: session.questionTimeLimit || undefined,
          autoAdvance: session.autoAdvance
        })
      } else {
        // Quiz completed
        await this.completeQuiz(sessionId)
      }

    } catch (error) {
      console.error('Error advancing question:', error)
      throw error
    }
  }

  /**
   * Handle answer submission with real-time feedback
   */
  async processAnswerSubmission(
    sessionId: string,
    userId: string,
    questionId: string,
    answer: any,
    timeSpent: number
  ): Promise<void> {
    try {
      const socketClient = getSocketClient()

      // Update participant progress
      this.updateParticipantProgress(sessionId, userId, {
        timeSpent,
        lastActivity: new Date()
      })

      // Broadcast answer submission (without revealing correctness)
      socketClient.broadcastLiveQuizEvent('live-quiz:answer-received', {
        sessionId,
        participantId: userId,
        questionId,
        submittedAt: new Date(),
        timeSpent
      })

      // Update real-time statistics
      await this.updateQuestionStatistics(sessionId, questionId)

    } catch (error) {
      console.error('Error processing answer submission:', error)
      throw error
    }
  }

  /**
   * Synchronize participant progress
   */
  async syncParticipantProgress(
    sessionId: string,
    userId: string,
    progress: Partial<ParticipantProgress>
  ): Promise<void> {
    this.updateParticipantProgress(sessionId, userId, progress)

    const socketClient = getSocketClient()
    socketClient.emit('live-quiz:progress-synced', {
      sessionId,
      userId,
      progress,
      syncedAt: new Date()
    })
  }

  /**
   * Get real-time session statistics
   */
  async getSessionStatistics(sessionId: string): Promise<any> {
    try {
      const participants = await prisma.liveQuizParticipant.findMany({
        where: { sessionId, isActive: true },
        select: {
          userId: true,
          score: true,
          currentQuestion: true,
          totalAnswered: true,
          timeSpent: true
        }
      })

      const progress = this.participantProgress.get(sessionId) || new Map()

      return {
        totalParticipants: participants.length,
        activeParticipants: Array.from(progress.values()).filter(
          p => Date.now() - p.lastActivity.getTime() < 30000 // Active in last 30 seconds
        ).length,
        averageScore: participants.length > 0 
          ? Math.round(participants.reduce((sum, p) => sum + p.score, 0) / participants.length)
          : 0,
        averageProgress: participants.length > 0
          ? Math.round(participants.reduce((sum, p) => sum + p.currentQuestion, 0) / participants.length)
          : 0,
        completionRate: participants.length > 0
          ? Math.round((participants.filter(p => p.totalAnswered > 0).length / participants.length) * 100)
          : 0
      }
    } catch (error) {
      console.error('Error getting session statistics:', error)
      return null
    }
  }

  /**
   * Complete the quiz and calculate final results
   */
  private async completeQuiz(sessionId: string): Promise<void> {
    try {
      // Clear any timers
      this.clearAutoAdvanceTimer(sessionId)
      this.stopProgressMonitoring(sessionId)

      // Calculate final rankings
      const participants = await prisma.liveQuizParticipant.findMany({
        where: { sessionId, isActive: true },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: [
          { score: 'desc' },
          { totalAnswered: 'desc' },
          { timeSpent: 'asc' }
        ]
      })

      // Update final rankings
      for (let i = 0; i < participants.length; i++) {
        await prisma.liveQuizParticipant.update({
          where: { id: participants[i].id },
          data: { rank: i + 1 }
        })
      }

      // Update session status
      await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: {
          status: 'COMPLETED',
          endTime: new Date()
        }
      })

      // Broadcast completion
      const socketClient = getSocketClient()
      socketClient.emit('live-quiz:quiz-completed', {
        sessionId,
        finalRankings: participants.map((p, index) => ({
          userId: p.userId,
          userName: p.user.name,
          score: p.score,
          rank: index + 1,
          correctAnswers: p.correctAnswers,
          totalAnswered: p.totalAnswered,
          timeSpent: p.timeSpent
        })),
        completedAt: new Date()
      })

    } catch (error) {
      console.error('Error completing quiz:', error)
      throw error
    }
  }

  // Helper methods
  private async getTotalQuestions(quizId: string): Promise<number> {
    const count = await prisma.question.count({
      where: { quizId }
    })
    return count
  }

  private initializeParticipantProgress(sessionId: string, participants: any[]): void {
    const progressMap = new Map<string, ParticipantProgress>()
    
    participants.forEach(participant => {
      progressMap.set(participant.userId, {
        userId: participant.userId,
        currentQuestion: participant.currentQuestion,
        score: participant.score,
        timeSpent: participant.timeSpent,
        lastActivity: new Date()
      })
    })

    this.participantProgress.set(sessionId, progressMap)
  }

  private updateParticipantProgress(
    sessionId: string,
    userId: string,
    updates: Partial<ParticipantProgress>
  ): void {
    const sessionProgress = this.participantProgress.get(sessionId)
    if (!sessionProgress) return

    const current = sessionProgress.get(userId)
    if (current) {
      sessionProgress.set(userId, { ...current, ...updates })
    }
  }

  private scheduleAutoAdvance(sessionId: string, timeLimit: number): void {
    const timer = setTimeout(async () => {
      try {
        await this.advanceQuestion(sessionId)
      } catch (error) {
        console.error('Error in auto-advance:', error)
      }
    }, timeLimit * 1000)

    this.activeTimers.set(sessionId, timer)
  }

  private clearAutoAdvanceTimer(sessionId: string): void {
    const timer = this.activeTimers.get(sessionId)
    if (timer) {
      clearTimeout(timer)
      this.activeTimers.delete(sessionId)
    }
  }

  private startProgressMonitoring(sessionId: string): void {
    // Monitor participant activity and sync progress every 10 seconds
    const monitoringTimer = setInterval(async () => {
      try {
        const stats = await this.getSessionStatistics(sessionId)
        if (stats) {
          const socketClient = getSocketClient()
          socketClient.emit('live-quiz:statistics-updated', {
            sessionId,
            statistics: stats,
            updatedAt: new Date()
          })
        }
      } catch (error) {
        console.error('Error in progress monitoring:', error)
      }
    }, 10000)

    this.activeTimers.set(`${sessionId}-monitor`, monitoringTimer)
  }

  private stopProgressMonitoring(sessionId: string): void {
    this.clearAutoAdvanceTimer(`${sessionId}-monitor`)
    this.participantProgress.delete(sessionId)
  }

  private async updateQuestionStatistics(sessionId: string, questionId: string): Promise<void> {
    try {
      const answers = await prisma.liveQuizParticipant.findMany({
        where: { sessionId, isActive: true },
        select: { answers: true }
      })

      let answeredCount = 0
      const answerDistribution: Record<string, number> = {}

      answers.forEach(participant => {
        const participantAnswers = participant.answers as Record<string, any>
        const answer = participantAnswers[questionId]
        
        if (answer !== undefined) {
          answeredCount++
          const answerKey = String(answer)
          answerDistribution[answerKey] = (answerDistribution[answerKey] || 0) + 1
        }
      })

      const socketClient = getSocketClient()
      socketClient.emit('live-quiz:question-statistics', {
        sessionId,
        questionId,
        statistics: {
          totalParticipants: answers.length,
          answeredCount,
          answerDistribution,
          answerRate: answers.length > 0 ? Math.round((answeredCount / answers.length) * 100) : 0
        },
        updatedAt: new Date()
      })

    } catch (error) {
      console.error('Error updating question statistics:', error)
    }
  }
}

// Export singleton instance
export const liveQuizDelivery = LiveQuizDeliveryService.getInstance()
